import React from "react";
import {
  Box,
  Button,
  Checkbox,
  Divider,
  FormControlLabel,
  Grid,
  InputAdornment,
  Stack,
  TextField,
  Typography,
  Autocomplete,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import {
  Reservation,
  EditedValues,
  EditableFields,
  UpdateReservationReportDto,
  UpdateReservationJobDto
} from "src/common/types";
import { CePaper } from "src/common/components";
import { useReservationCost } from "src/common/api/reservation";
import { useUpdateJob, useUpdateReport } from "src/common/api/job";
import { useUpdateCompany } from "src/common/api/company";
import { turnCompanyFormValuesIntoUpdateDto } from "src/common/utils/company";
import { Tax } from "src/common/types/company";
import { JobStatus } from "src/common/types/job";
import { INITIAL_EDITED_VALUES } from "src/common/constants/reservation";

interface DetailsSectionProps {
  reservation: Reservation | undefined;
}
const GridHeader = ({
  children,
  xs = 6,
  sm = 2,
  align = "left",
}: {
  children: React.ReactNode;
  xs?: number;
  sm?: number;
  align?: "left" | "right";
}) => (
  <Grid item xs={xs} sm={sm}>
    <Typography variant="body2" fontWeight="bold" align={align}>
      {children}
    </Typography>
  </Grid>
);

const GridItem = ({
  children,
  xs = 6,
  sm = 2,
  align = "left",
  color,
}: {
  children: React.ReactNode;
  xs?: number;
  sm?: number;
  align?: "left" | "right";
  color?: string;
}) => (
  <Grid item xs={xs} sm={sm}>
    <Typography variant="body2" align={align} color={color}>
      {children}
    </Typography>
  </Grid>
);

interface EditableGridItemProps {
  field: EditableFields;
  value: number | boolean;
  displayValue: string;
  isEditable?: boolean;
  editComponent: React.ReactNode;
  align?: "left" | "right";
  xs?: number;
  sm?: number;
  isEditing: boolean;
}

const EditableGridItem: React.FC<EditableGridItemProps> = ({
  field,
  value,
  displayValue,
  isEditable = true,
  editComponent,
  align = "right",
  xs = 6,
  sm = 2,
  isEditing,
}) => {
  return (
    <Grid item xs={xs} sm={sm}>
      <Stack
        direction="row"
        spacing={1}
        justifyContent={align === "right" ? "flex-end" : "flex-start"}
        alignItems="center"
      >
        {isEditing ? (
          <>{editComponent}</>
        ) : (
          <Typography variant="body2" align={align}>
            {displayValue}
          </Typography>
        )}
      </Stack>
    </Grid>
  );
};

const DetailsSection: React.FC<DetailsSectionProps> = ({ reservation }) => {
  const { t } = useTranslation("common");
  const reservationJob = reservation?.job;
  const reservationJobReport = reservationJob?.report;
  const reservationPriceList = reservation?.pricelist;

  const [isEditing, setIsEditing] = React.useState(false);

  const [editedValues, setEditedValues] =
    React.useState<EditedValues>(INITIAL_EDITED_VALUES);

  const [selectedTax, setSelectedTax] = React.useState<Tax | null>(null);

  React.useEffect(() => {
    setEditedValues({
      amountOfConcrete: reservationJobReport?.amountOfConcrete || 0,
      flexiblePipeLength80Mm: reservationJobReport?.flexiblePipeLength80Mm || 0,
      flexiblePipeLength90Mm: reservationJobReport?.flexiblePipeLength90Mm || 0,
      rigidPipeLength100Mm: reservationJobReport?.rigidPipeLength100Mm || 0,
      rigidPipeLength120Mm: reservationJobReport?.rigidPipeLength120Mm || 0,
      extraCementBags: reservationJobReport?.extraCementBags || false,
      cementBags: reservationJobReport?.cementBags || 0,
      secondTechnician: reservationJobReport?.secondTechnician || false,
      cleaning: reservation?.job?.cleaning === "Central" ? "Central" : null,
      barbotine: reservation?.job?.barbotine || false,
      supplyOfTheChemicalSlushie:
        reservation?.job?.supplyOfTheChemicalSlushie || false,
      units: reservation?.job?.units || 0,
    });
  }, [reservationJobReport, reservation]);

  React.useEffect(() => {
    if (reservation?.manager?.company?.taxes) {
      const taxes = reservation.manager.company.taxes as Tax[];
      const defaultTax = taxes.find((tax) => tax.isDefault);
      setSelectedTax(defaultTax || taxes[0] || null);
    }
  }, [reservation?.manager?.company?.taxes]);

  const { mutateAsync: updateJobAsync } = useUpdateJob();
  const { mutateAsync: updateReportAsync } = useUpdateReport();
  const { mutateAsync: updateCompanyAsync } = useUpdateCompany();

  const { data: reservationCost, refetch: refetchCost } = useReservationCost(
    reservation?.id,
    Boolean(reservation?.id)
  );

  const startEditing = () => {
    setIsEditing(true);
  };

  const cancelEditing = () => {
    setEditedValues({
      amountOfConcrete: reservationJobReport?.amountOfConcrete || 0,
      flexiblePipeLength80Mm: reservationJobReport?.flexiblePipeLength80Mm || 0,
      flexiblePipeLength90Mm: reservationJobReport?.flexiblePipeLength90Mm || 0,
      rigidPipeLength100Mm: reservationJobReport?.rigidPipeLength100Mm || 0,
      rigidPipeLength120Mm: reservationJobReport?.rigidPipeLength120Mm || 0,
      extraCementBags: reservationJobReport?.extraCementBags || false,
      cementBags: reservationJobReport?.cementBags || 0,
      secondTechnician: reservationJobReport?.secondTechnician || false,
      cleaning: reservation?.job?.cleaning === "Central" ? "Central" : null,
      barbotine: reservation?.job?.barbotine || false,
      supplyOfTheChemicalSlushie:
        reservation?.job?.supplyOfTheChemicalSlushie || false,
      units: reservation?.job?.units || 0,
    });
    setIsEditing(false);
  };

  const saveChanges = async () => {
    if (!reservationJob?.id) return;

    try {
      const updateReportDto: UpdateReservationReportDto = {
        jobId: Number(reservationJob.id),
        amountOfConcrete: editedValues.amountOfConcrete,
        flexiblePipeLength80Mm: editedValues.flexiblePipeLength80Mm,
        flexiblePipeLength90Mm: editedValues.flexiblePipeLength90Mm,
        rigidPipeLength100Mm: editedValues.rigidPipeLength100Mm,
        rigidPipeLength120Mm: editedValues.rigidPipeLength120Mm,
        extraCementBags: editedValues.extraCementBags,
        cementBags: editedValues.cementBags,
        secondTechnician: editedValues.secondTechnician,
      };

      const updateJobDto: UpdateReservationJobDto = {
        jobId: Number(reservationJob.id),
        reservationId: reservation?.id ? Number(reservation.id) : undefined,
        cleaning: editedValues.cleaning,
        barbotine: editedValues.barbotine,
        supplyOfTheChemicalSlushie: editedValues.supplyOfTheChemicalSlushie,
        units: editedValues.units,
      };
      await updateReportAsync(updateReportDto);
      await updateJobAsync(updateJobDto);

      if (selectedTax && reservation?.manager?.company) {
        const company = reservation.manager.company;
        const currentTaxes = company.taxes as Tax[];

        const currentDefaultTax = currentTaxes.find((tax) => tax.isDefault);
        const selectedTaxMatches =
          currentDefaultTax &&
          currentDefaultTax.name === selectedTax.name &&
          currentDefaultTax.percentage === selectedTax.percentage;

        if (!selectedTaxMatches) {
          const updatedTaxes = currentTaxes.map((tax) => ({
            ...tax,
            isDefault:
              tax.name === selectedTax.name &&
              tax.percentage === selectedTax.percentage,
          }));

          const companyUpdateDto = turnCompanyFormValuesIntoUpdateDto({
            companyId: company.id,
            name: company.name || "",
            type: company.type || "",
            contactEmail: company.contactEmail || "",
            billingEmail: company.billingEmail || "",
            planningEmail: company.planningEmail || "",
            phoneNumber: company.phoneNumber || "",
            address: company.address || "",
            secondaryAddress: company.secondaryAddress || "",
            country: null,
            city: company.city || "",
            zipCode: company.zipCode || null,
            vatNumber: company.vatNumber || "",
            taxes: updatedTaxes,
          });

          await updateCompanyAsync(companyUpdateDto);
        }
      }

      refetchCost();
      setIsEditing(false);
    } catch (error) {
      console.error("Error saving changes:", error);
      refetchCost();
      setIsEditing(false);
    }
  };

  const handleFieldChange = (
    field: EditableFields,
    value: number | boolean
  ) => {
    if (!reservationJob?.id) return;
    setEditedValues((prev) => ({ ...prev, [field]: value }));
  };

  const vatRate = selectedTax?.percentage || 21;
  const totalExclVat = reservationCost?.totalCost || 0;
  const vatAmount = (totalExclVat * vatRate) / 100;
  const totalAmount = totalExclVat + vatAmount;

  const availableTaxes = (reservation?.manager?.company?.taxes as Tax[]) || [];
  return (
    <Box
      component="section"
      display="flex"
      flexDirection="column"
      gap={1}
      sx={{ width: "100%" }}
    >
      <CePaper
        sx={{
          padding: 3,
          marginBottom: 2,
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            mb: 2,
          }}
        >
          <Typography variant="h6" sx={{ fontWeight: "bold" }}>
            {t("details")}
          </Typography>
          {isEditing ? (
            <Box>
              <Button
                variant="contained"
                color="primary"
                onClick={saveChanges}
                sx={{ mr: 1 }}
              >
                {t("save")}
              </Button>
              <Button variant="outlined" onClick={cancelEditing}>
                {t("cancel")}
              </Button>
            </Box>
          ) : (
            !reservation?.invoiced &&
            reservation?.job?.status === JobStatus.COMPLETE && (
              <Button
                variant="contained"
                color="primary"
                onClick={startEditing}
              >
                {t("edit")}
              </Button>
            )
          )}
        </Box>
        <Typography variant="body2">
          COLAS - COLAS (No. FR281241264128712)
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Rue de Charleville ⇒ Rue de Bastion
        </Typography>

        <Box sx={{ mt: 2 }}>
          <Grid container spacing={1}>
            <GridHeader>Service</GridHeader>
            <GridHeader align="right">Planned</GridHeader>
            <GridHeader align="right">Actual</GridHeader>
            <GridHeader align="right">{t("invoiced")}</GridHeader>
            <GridHeader align="right">{t("cost")}</GridHeader>
            <GridHeader align="right">{t("sum")}</GridHeader>
          </Grid>
          <Divider sx={{ my: 1 }} />
          {(reservationJobReport?.amountOfConcrete || 0) <
          (reservationPriceList?.minimumM3Charged || 0) ? (
            <Grid container spacing={1} sx={{ py: 0.5 }}>
              <GridItem color="text.secondary">
                {t("minimum-package-flat-fee")}
              </GridItem>
              <GridItem align="right">
                {reservationCost?.baseFee ? 1 : "-"}
              </GridItem>
              <GridItem align="right">
                {reservationCost?.baseFee ? 1 : "-"}
              </GridItem>
              <GridItem align="right">
                {reservationCost?.baseFee ? 1 : "-"}
              </GridItem>
              <GridItem align="right">
                {reservationPriceList?.minimumChargeFlatFee?.toFixed(2) ||
                  "0.00"}{" "}
                €
              </GridItem>
              <GridItem align="right">
                {reservationCost?.baseFee
                  ? reservationCost.baseFee.toFixed(2)
                  : "0.00"}{" "}
                €
              </GridItem>
            </Grid>
          ) : (
            <>
              {(reservationCost?.baseHourBreakdown?.work ?? 0) > 0 && (
                <Grid container spacing={1} sx={{ py: 0.5 }}>
                  <GridItem color="text.secondary">
                    {t("package-flat-fee")}
                  </GridItem>
                  <GridItem align="right">
                    {reservationCost.baseHourBreakdown.work}
                  </GridItem>
                  <GridItem align="right">
                    {reservationCost.baseHourBreakdown.work}
                  </GridItem>
                  <GridItem align="right">
                    {reservationCost.baseHourBreakdown.work}
                  </GridItem>
                  <GridItem align="right">
                    {reservationPriceList?.packageFlatFee?.toFixed(2) || "0.00"}{" "}
                    €
                  </GridItem>
                  <GridItem align="right">
                    {(
                      (reservationCost.baseHourBreakdown.work /
                        (reservationPriceList?.packageFlatFeeDuration || 0)) *
                      (reservationPriceList?.packageFlatFee || 0)
                    ).toFixed(2)}{" "}
                    €
                  </GridItem>
                </Grid>
              )}
              {(reservationCost?.baseHourBreakdown?.night ?? 0) > 0 && (
                <Grid container spacing={1} sx={{ py: 0.5 }}>
                  <GridItem color="text.secondary">
                    {t("night-flat-fee")}
                  </GridItem>
                  <GridItem align="right">
                    {reservationCost.baseHourBreakdown.night}
                  </GridItem>
                  <GridItem align="right">
                    {reservationCost.baseHourBreakdown.night}
                  </GridItem>
                  <GridItem align="right">
                    {reservationCost.baseHourBreakdown.night}
                  </GridItem>
                  <GridItem align="right">
                    {reservationPriceList?.packageFlatFeeNight?.toFixed(2) ||
                      reservationPriceList?.packageFlatFee?.toFixed(2) ||
                      "0.00"}{" "}
                    €
                  </GridItem>
                  <GridItem align="right">
                    {(
                      (reservationCost.baseHourBreakdown.night /
                        (reservationPriceList?.packageFlatFeeDuration || 0)) *
                      (reservationPriceList?.packageFlatFeeNight ??
                        (reservationPriceList?.packageFlatFee || 0))
                    ).toFixed(2)}{" "}
                    €
                  </GridItem>
                </Grid>
              )}
              {(reservationCost?.baseHourBreakdown?.weekend ?? 0) > 0 && (
                <Grid container spacing={1} sx={{ py: 0.5 }}>
                  <GridItem color="text.secondary">
                    {t("weekend-flat-fee")}
                  </GridItem>
                  <GridItem align="right">
                    {reservationCost.baseHourBreakdown.weekend}
                  </GridItem>
                  <GridItem align="right">
                    {reservationCost.baseHourBreakdown.weekend}
                  </GridItem>
                  <GridItem align="right">
                    {reservationCost.baseHourBreakdown.weekend}
                  </GridItem>
                  <GridItem align="right">
                    {reservationPriceList?.packageFlatFeeWeekend?.toFixed(2) ||
                      reservationPriceList?.packageFlatFee?.toFixed(2) ||
                      0}{" "}
                    €
                  </GridItem>
                  <GridItem align="right">
                    {(
                      (reservationCost.baseHourBreakdown.weekend /
                        (reservationPriceList?.packageFlatFeeDuration || 0)) *
                      (reservationPriceList?.packageFlatFeeWeekend ??
                        (reservationPriceList?.packageFlatFee || 0))
                    ).toFixed(2)}{" "}
                    €
                  </GridItem>
                </Grid>
              )}
            </>
          )}

          {reservationCost?.concreteCost !== 0 && (
            <Grid container spacing={1} sx={{ py: 0.5 }}>
              <GridItem color="text.secondary">
                {t("price-per-m³-pumped")}
              </GridItem>
              <GridItem align="right">
                {reservationJob?.amountOfConcrete
                  ? `${reservationJob?.amountOfConcrete} m³`
                  : "-"}
              </GridItem>
              <EditableGridItem
                field="amountOfConcrete"
                value={editedValues.amountOfConcrete}
                displayValue={`${reservationJob?.amountOfConcrete || 0} m³`}
                editComponent={
                  <TextField
                    type="number"
                    size="small"
                    value={editedValues.amountOfConcrete || ""}
                    onChange={(e) =>
                      handleFieldChange(
                        "amountOfConcrete",
                        e.target.value ? Number(e.target.value) : 0
                      )
                    }
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">m³</InputAdornment>
                      ),
                    }}
                    sx={{ width: 100 }}
                  />
                }
                isEditing={isEditing}
              />
              <GridItem align="right">
                {reservationJobReport?.amountOfConcrete
                  ? `${reservationJobReport?.amountOfConcrete} m³`
                  : "-"}
              </GridItem>
              <GridItem align="right">
                {reservationJobReport?.amountOfConcrete
                  ? `${reservationJobReport?.amountOfConcrete} m³`
                  : "-"}
              </GridItem>
              <GridItem align="right">
                {reservationPriceList?.pricePerMeterPumped
                  ? reservationPriceList?.pricePerMeterPumped.toFixed(2)
                  : 0.0}{" "}
                €
              </GridItem>
              <GridItem align="right">
                {reservationCost?.concreteCost?.toFixed(2) || "0.00"} €
              </GridItem>
            </Grid>
          )}
          {reservationPriceList?.pumpTiers &&
            reservationPriceList.pumpTiers.length > 0 && (
              <Grid container spacing={1} sx={{ py: 0.5 }}>
                <GridItem color="text.secondary">Concrete Amount</GridItem>
                <GridItem align="right">
                  {reservationJob?.amountOfConcrete || 0} m³
                </GridItem>
                <EditableGridItem
                  field="amountOfConcrete"
                  value={editedValues.amountOfConcrete}
                  displayValue={`${
                    reservationJobReport?.amountOfConcrete || 0
                  } m`}
                  editComponent={
                    <TextField
                      type="number"
                      size="small"
                      value={editedValues.amountOfConcrete || ""}
                      onChange={(e) =>
                        handleFieldChange(
                          "amountOfConcrete",
                          e.target.value ? Number(e.target.value) : 0
                        )
                      }
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">m</InputAdornment>
                        ),
                      }}
                      sx={{ width: 100 }}
                    />
                  }
                  isEditing={isEditing}
                />
                <GridItem align="right">
                  {reservationJobReport?.amountOfConcrete || 0} m³
                </GridItem>
                <GridItem align="right">
                  {(
                    (reservationCost?.pumpTiersCost ?? 0) /
                    (reservationJobReport?.amountOfConcrete ?? 1)
                  ).toFixed(2)}{" "}
                  €
                </GridItem>
                <GridItem align="right">
                  {reservationCost?.pumpTiersCost.toFixed(2) || 0} €
                </GridItem>
              </Grid>
            )}
          <Grid container spacing={1} sx={{ py: 0.5 }}>
            <GridItem color="text.secondary">{t("flexible-pipe-80")}</GridItem>
            <GridItem align="right">
              {reservationJob?.flexiblePipeLength80Mm
                ? `${reservationJob.flexiblePipeLength80Mm} m`
                : "-"}
            </GridItem>
            <EditableGridItem
              field="flexiblePipeLength80Mm"
              value={editedValues.flexiblePipeLength80Mm}
              displayValue={`${
                reservationJobReport?.flexiblePipeLength80Mm || 0
              } m`}
              editComponent={
                <TextField
                  type="number"
                  size="small"
                  value={editedValues.flexiblePipeLength80Mm || ""}
                  onChange={(e) =>
                    handleFieldChange(
                      "flexiblePipeLength80Mm",
                      e.target.value ? Number(e.target.value) : 0
                    )
                  }
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">m</InputAdornment>
                    ),
                  }}
                  sx={{ width: 100 }}
                />
              }
              isEditing={isEditing}
            />
            <GridItem align="right">
              {reservationJobReport?.flexiblePipeLength80Mm
                ? `${reservationJobReport?.flexiblePipeLength80Mm} m`
                : "-"}
            </GridItem>
            <GridItem align="right">
              {reservationPriceList?.pricePerMeterOfFlexiblePipeLength80Mm
                ? reservationPriceList?.pricePerMeterOfFlexiblePipeLength80Mm.toFixed(
                    2
                  )
                : 0.0}{" "}
              €
            </GridItem>
            <GridItem align="right">
              {reservationCost?.pipe80mmCost.toFixed(2)} €
            </GridItem>
          </Grid>
          <Grid container spacing={1} sx={{ py: 0.5 }}>
            <GridItem color="text.secondary">{t("flexible-pipe-90")}</GridItem>
            <GridItem align="right">
              {reservationJob?.flexiblePipeLength90Mm
                ? `${reservationJob?.flexiblePipeLength90Mm} m`
                : "-"}
            </GridItem>
            <EditableGridItem
              field="flexiblePipeLength90Mm"
              value={editedValues.flexiblePipeLength90Mm}
              displayValue={`${
                reservationJobReport?.flexiblePipeLength90Mm || 0
              } m`}
              editComponent={
                <TextField
                  type="number"
                  size="small"
                  value={editedValues.flexiblePipeLength90Mm || ""}
                  onChange={(e) =>
                    handleFieldChange(
                      "flexiblePipeLength90Mm",
                      e.target.value ? Number(e.target.value) : 0
                    )
                  }
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">m</InputAdornment>
                    ),
                  }}
                  sx={{ width: 100 }}
                />
              }
              isEditing={isEditing}
            />
            <GridItem align="right">
              {reservationJobReport?.flexiblePipeLength90Mm
                ? `${reservationJobReport?.flexiblePipeLength90Mm} m`
                : "-"}
            </GridItem>
            <GridItem align="right">
              {reservationPriceList?.pricePerMeterOfFlexiblePipeLength90Mm
                ? reservationPriceList?.pricePerMeterOfFlexiblePipeLength90Mm.toFixed(
                    2
                  )
                : 0.0}{" "}
              €
            </GridItem>
            <GridItem align="right">
              {reservationCost?.pipe90mmCost.toFixed(2)} €
            </GridItem>
          </Grid>
          <Grid container spacing={1} sx={{ py: 0.5 }}>
            <GridItem color="text.secondary">{t("rigid-pipe-100")}</GridItem>
            <GridItem align="right">
              {reservationJob?.rigidPipeLength100Mm
                ? `${reservationJob?.rigidPipeLength100Mm} m`
                : "-"}
            </GridItem>
            <EditableGridItem
              field="rigidPipeLength100Mm"
              value={editedValues.rigidPipeLength100Mm}
              displayValue={`${
                reservationJobReport?.rigidPipeLength100Mm || 0
              } m`}
              editComponent={
                <TextField
                  type="number"
                  size="small"
                  value={editedValues.rigidPipeLength100Mm || ""}
                  onChange={(e) =>
                    handleFieldChange(
                      "rigidPipeLength100Mm",
                      e.target.value ? Number(e.target.value) : 0
                    )
                  }
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">m</InputAdornment>
                    ),
                  }}
                  sx={{ width: 100 }}
                />
              }
              isEditing={isEditing}
            />
            <GridItem align="right">
              {reservationJobReport?.rigidPipeLength100Mm
                ? `${reservationJobReport?.rigidPipeLength100Mm} m`
                : "-"}
            </GridItem>
            <GridItem align="right">
              {reservationPriceList?.pricePerMeterOfFlexiblePipeLength100Mm
                ? reservationPriceList?.pricePerMeterOfFlexiblePipeLength100Mm.toFixed(
                    2
                  )
                : 0.0}{" "}
              €
            </GridItem>
            <GridItem align="right">
              {reservationCost?.pipe100mmCost.toFixed(2)} €
            </GridItem>
          </Grid>
          <Grid container spacing={1} sx={{ py: 0.5 }}>
            <GridItem color="text.secondary">{t("rigid-pipe-120")}</GridItem>
            <GridItem align="right">
              {reservationJob?.rigidPipeLength120Mm
                ? `${reservationJob?.rigidPipeLength120Mm} m`
                : "-"}
            </GridItem>
            <EditableGridItem
              field="rigidPipeLength120Mm"
              value={editedValues.rigidPipeLength120Mm}
              displayValue={`${
                reservationJobReport?.rigidPipeLength120Mm || 0
              } m`}
              editComponent={
                <TextField
                  type="number"
                  size="small"
                  value={editedValues.rigidPipeLength120Mm || ""}
                  onChange={(e) =>
                    handleFieldChange(
                      "rigidPipeLength120Mm",
                      e.target.value ? Number(e.target.value) : 0
                    )
                  }
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">m</InputAdornment>
                    ),
                  }}
                  sx={{ width: 100 }}
                />
              }
              isEditing={isEditing}
            />
            <GridItem align="right">
              {reservationJobReport?.rigidPipeLength120Mm
                ? `${reservationJobReport?.rigidPipeLength120Mm} m`
                : "-"}
            </GridItem>
            <GridItem align="right">
              {reservationPriceList?.pricePerMeterOfRigidPipeLength120Mm
                ? reservationPriceList?.pricePerMeterOfRigidPipeLength120Mm.toFixed(
                    2
                  )
                : 0.0}{" "}
              €
            </GridItem>
            <GridItem align="right">
              {reservationCost?.pipe120mmCost.toFixed(2)} €
            </GridItem>
          </Grid>
          {(reservationCost?.additionalHours ?? 0) > 0 && (
            <>
              {(reservationCost?.additionalHourBreakdown?.work ?? 0) > 0 && (
                <Grid container spacing={1} sx={{ py: 0.5 }}>
                  <GridItem color="text.secondary">
                    {t("additional-hour")}
                  </GridItem>
                  <GridItem align="right">
                    {reservationCost?.additionalHourBreakdown.work.toFixed(2)}
                  </GridItem>
                  <GridItem align="right">
                    {reservationCost?.additionalHourBreakdown.work.toFixed(2)}
                  </GridItem>
                  <GridItem align="right">
                    {reservationCost?.additionalHourBreakdown.work.toFixed(2)}
                  </GridItem>
                  <GridItem align="right">
                    {reservationPriceList?.additionalHour?.toFixed(2) || "0.00"}{" "}
                    €
                  </GridItem>
                  <GridItem align="right">
                    {(
                      reservationCost?.additionalHourBreakdown.work *
                      (reservationPriceList?.additionalHour || 0)
                    ).toFixed(2)}{" "}
                    €
                  </GridItem>
                </Grid>
              )}

              {(reservationCost?.additionalHourBreakdown?.night ?? 0) > 0 && (
                <Grid container spacing={1} sx={{ py: 0.5 }}>
                  <GridItem color="text.secondary">{t("night-hour")}</GridItem>
                  <GridItem align="right">
                    {reservationCost.additionalHourBreakdown.night.toFixed(2)}
                  </GridItem>
                  <GridItem align="right">
                    {reservationCost.additionalHourBreakdown.night.toFixed(2)}
                  </GridItem>
                  <GridItem align="right">
                    {reservationCost.additionalHourBreakdown.night.toFixed(2)}
                  </GridItem>
                  <GridItem align="right">
                    {(
                      reservationPriceList?.additionalHourNight ??
                      reservationPriceList?.additionalHour ??
                      0
                    ).toFixed(2)}{" "}
                    €
                  </GridItem>
                  <GridItem align="right">
                    {(
                      reservationCost.additionalHourBreakdown.night *
                      (reservationPriceList?.additionalHourNight ??
                        reservationPriceList?.additionalHour ??
                        0)
                    ).toFixed(2)}{" "}
                    €
                  </GridItem>
                </Grid>
              )}

              {(reservationCost?.additionalHourBreakdown?.weekend ?? 0) > 0 && (
                <Grid container spacing={1} sx={{ py: 0.5 }}>
                  <GridItem color="text.secondary">
                    {t("weekend-additional-hour")}
                  </GridItem>
                  <GridItem align="right">
                    {reservationCost.additionalHourBreakdown.weekend.toFixed(2)}
                  </GridItem>
                  <GridItem align="right">
                    {reservationCost.additionalHourBreakdown.weekend.toFixed(2)}
                  </GridItem>
                  <GridItem align="right">
                    {reservationCost.additionalHourBreakdown.weekend.toFixed(2)}
                  </GridItem>
                  <GridItem align="right">
                    {(
                      reservationPriceList?.additionalHourWeekend ??
                      reservationPriceList?.additionalHour ??
                      0
                    ).toFixed(2)}{" "}
                    €
                  </GridItem>
                  <GridItem align="right">
                    {(
                      reservationCost.additionalHourBreakdown.weekend *
                      (reservationPriceList?.additionalHourWeekend ??
                        reservationPriceList?.additionalHour ??
                        0)
                    ).toFixed(2)}{" "}
                    €
                  </GridItem>
                </Grid>
              )}
            </>
          )}

          {reservationJob?.enlistSecondTechnician &&
            reservationJobReport?.secondTechnician && (
              <>
                {(reservationCost?.secondTechnicianHourBreakdown?.work ?? 0) >
                  0 && (
                  <Grid container spacing={1} sx={{ py: 0.5 }}>
                    <GridItem color="text.secondary">
                      {t("2nd technician fee")}
                    </GridItem>
                    <GridItem align="right">
                      {reservationCost.secondTechnicianHourBreakdown.work.toFixed(
                        2
                      )}
                    </GridItem>
                    <GridItem align="right">
                      {reservationCost.secondTechnicianHourBreakdown.work.toFixed(
                        2
                      )}
                    </GridItem>
                    <GridItem align="right">
                      {reservationCost.secondTechnicianHourBreakdown.work.toFixed(
                        2
                      )}
                    </GridItem>
                    <GridItem align="right">
                      {reservationPriceList?.secondTechnicianHourFee
                        ? reservationPriceList.secondTechnicianHourFee.toFixed(
                            2
                          )
                        : "0.00"}{" "}
                      €
                    </GridItem>
                    <GridItem align="right">
                      {(
                        reservationCost.secondTechnicianHourBreakdown.work *
                        (reservationPriceList?.secondTechnicianHourFee || 0)
                      ).toFixed(2)}{" "}
                      €
                    </GridItem>
                  </Grid>
                )}

                {(reservationCost?.secondTechnicianHourBreakdown?.night ?? 0) >
                  0 && (
                  <Grid container spacing={1} sx={{ py: 0.5 }}>
                    <GridItem color="text.secondary">
                      {t("second-technician-fee-night")}
                    </GridItem>
                    <GridItem align="right">
                      {reservationCost.secondTechnicianHourBreakdown.night.toFixed(
                        2
                      )}
                    </GridItem>
                    <GridItem align="right">
                      {reservationCost.secondTechnicianHourBreakdown.night.toFixed(
                        2
                      )}
                    </GridItem>
                    <GridItem align="right">
                      {reservationCost.secondTechnicianHourBreakdown.night.toFixed(
                        2
                      )}
                    </GridItem>
                    <GridItem align="right">
                      {(
                        reservationPriceList?.secondTechnicianHourFeeNight ??
                        (reservationPriceList?.secondTechnicianHourFee || 0)
                      ).toFixed(2)}{" "}
                      €
                    </GridItem>
                    <GridItem align="right">
                      {(
                        reservationCost.secondTechnicianHourBreakdown.night *
                        (reservationPriceList?.secondTechnicianHourFeeNight ??
                          (reservationPriceList?.secondTechnicianHourFee || 0))
                      ).toFixed(2)}{" "}
                      €
                    </GridItem>
                  </Grid>
                )}

                {(reservationCost?.secondTechnicianHourBreakdown?.weekend ??
                  0) > 0 && (
                  <Grid container spacing={1} sx={{ py: 0.5 }}>
                    <GridItem color="text.secondary">
                      {t("second-technician-fee-weekend")}
                    </GridItem>
                    <GridItem align="right">
                      {reservationCost.secondTechnicianHourBreakdown.weekend.toFixed(
                        2
                      )}
                    </GridItem>
                    <GridItem align="right">
                      {reservationCost.secondTechnicianHourBreakdown.weekend.toFixed(
                        2
                      )}
                    </GridItem>
                    <GridItem align="right">
                      {reservationCost.secondTechnicianHourBreakdown.weekend.toFixed(
                        2
                      )}
                    </GridItem>
                    <GridItem align="right">
                      {(
                        reservationPriceList?.secondTechnicianHourFeeWeekend ??
                        (reservationPriceList?.secondTechnicianHourFee || 0)
                      ).toFixed(2)}{" "}
                      €
                    </GridItem>
                    <GridItem align="right">
                      {(
                        reservationCost.secondTechnicianHourBreakdown.weekend *
                        (reservationPriceList?.secondTechnicianHourFeeWeekend ??
                          (reservationPriceList?.secondTechnicianHourFee || 0))
                      ).toFixed(2)}{" "}
                      €
                    </GridItem>
                  </Grid>
                )}
              </>
            )}

          <Grid container spacing={1} sx={{ py: 0.5 }}>
            <GridItem color="text.secondary">{t("barbotine")}</GridItem>
            <EditableGridItem
              field="barbotine"
              value={editedValues.barbotine}
              displayValue={editedValues.barbotine ? "Yes" : "No"}
              editComponent={
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={editedValues.barbotine}
                      onChange={(e) =>
                        handleFieldChange("barbotine", e.target.checked)
                      }
                      size="small"
                    />
                  }
                  label=""
                />
              }
              isEditing={isEditing}
            />
            <GridItem align="right">
              {reservationJob?.barbotine ? "Yes" : "No"}
            </GridItem>
            <GridItem align="right">
              {reservationJob?.barbotine ? "Yes" : "No"}
            </GridItem>
            <GridItem align="right">
              {reservationCost?.barbotineCost?.toFixed(2) || "0.00"} €
            </GridItem>
            <GridItem align="right">
              {reservationCost?.barbotineCost?.toFixed(2) || "0.00"} €
            </GridItem>
          </Grid>
          <Grid container spacing={1} sx={{ py: 0.5 }}>
            <GridItem color="text.secondary">
              {t("supply-of-the-chemical-slushie")}
            </GridItem>
            <EditableGridItem
              field="supplyOfTheChemicalSlushie"
              value={editedValues.supplyOfTheChemicalSlushie}
              displayValue={
                editedValues.supplyOfTheChemicalSlushie ? "Yes" : "No"
              }
              editComponent={
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={editedValues.supplyOfTheChemicalSlushie}
                      onChange={(e) =>
                        handleFieldChange(
                          "supplyOfTheChemicalSlushie",
                          e.target.checked
                        )
                      }
                      size="small"
                    />
                  }
                  label=""
                />
              }
              isEditing={isEditing}
            />
            <GridItem align="right">
              {reservationJob?.supplyOfTheChemicalSlushie ? "Yes" : "No"}
            </GridItem>
            <GridItem align="right">
              {reservationJob?.supplyOfTheChemicalSlushie ? "Yes" : "No"}
            </GridItem>
            <GridItem align="right">
              {reservationPriceList?.supplyOfTheChemicalSlushie?.toFixed(2) ||
                "0.00"}{" "}
              €
            </GridItem>
            <GridItem align="right">
              {reservationCost?.supplyOfTheChemicalSlushieCost?.toFixed(2) ||
                "0.00"}{" "}
              €
            </GridItem>
          </Grid>
          <Grid container spacing={1} sx={{ py: 0.5 }}>
            <GridItem color="text.secondary">{t("extra-cement-bags")}</GridItem>
            <GridItem align="right">
              {reservationJob?.extraCementBag
                ? `${reservationJob?.units} bag/s`
                : "-"}
            </GridItem>
            <EditableGridItem
              field="extraCementBags"
              value={editedValues.extraCementBags}
              displayValue={
                editedValues.extraCementBags
                  ? `${editedValues.cementBags} bag(s)`
                  : "No"
              }
              editComponent={
                <Box>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={editedValues.extraCementBags}
                        onChange={(e) =>
                          handleFieldChange("extraCementBags", e.target.checked)
                        }
                        size="small"
                      />
                    }
                    label=""
                  />
                  {editedValues.extraCementBags && (
                    <TextField
                      type="number"
                      size="small"
                      value={editedValues.cementBags || ""}
                      onChange={(e) =>
                        handleFieldChange(
                          "cementBags",
                          e.target.value ? Number(e.target.value) : 0
                        )
                      }
                      sx={{ width: 80, ml: 1 }}
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end"></InputAdornment>
                        ),
                      }}
                    />
                  )}
                </Box>
              }
              isEditing={isEditing}
            />
            <GridItem align="right">
              {reservationJobReport?.extraCementBags
                ? `${reservationJobReport?.cementBags} bag/s`
                : "-"}
            </GridItem>
            <GridItem align="right">
              {reservationPriceList?.extraCementBagPrice
                ? reservationPriceList?.extraCementBagPrice.toFixed(2)
                : "0.00"}{" "}
              €
            </GridItem>
            <GridItem align="right">
              {reservationCost?.extraCementBagCost?.toFixed(2) || "0.00"} €
            </GridItem>
          </Grid>
          {reservationJob?.cleaning === "Central" && (
            <Grid container spacing={1} sx={{ py: 0.5 }}>
              <GridItem color="text.secondary">{t("cleaning-fee")}</GridItem>
              <GridItem align="right">
                {reservationJob?.cleaning === "Central" ? 1 : "-"}
              </GridItem>
              <GridItem align="right">
                {reservationJob?.cleaning === "Central" ? 1 : "-"}
              </GridItem>
              <GridItem align="right">
                {reservationJob?.cleaning === "Central" ? 1 : "-"}
              </GridItem>
              <GridItem align="right">
                {reservationPriceList?.cleaningFee
                  ? reservationPriceList?.cleaningFee.toFixed(2)
                  : 0.0}{" "}
                €
              </GridItem>
              <GridItem align="right">
                {reservationCost?.cleaningFee.toFixed(2)} €
              </GridItem>
            </Grid>
          )}
          <Grid container spacing={1} sx={{ py: 0.5 }}>
            <GridItem color="text.secondary">{t("transport-rate")}</GridItem>
            <GridItem align="right">
              {reservationCost?.chargedTransportRate ? 1 : "-"}
            </GridItem>
            <GridItem align="right">
              {reservationCost?.chargedTransportRate ? 1 : "-"}
            </GridItem>
            <GridItem align="right">
              {reservationCost?.chargedTransportRate ? 1 : "-"}
            </GridItem>
            <GridItem align="right">
              {reservationCost?.chargedTransportRate
                ? reservationCost?.chargedTransportRate?.tariff?.toFixed(2)
                : "0.00"}{" "}
              €
            </GridItem>
            <GridItem align="right">
              {reservationCost?.chargedTransportRate?.tariff?.toFixed(2) ||
                "0.00"}{" "}
              €
            </GridItem>
          </Grid>
          <Divider sx={{ my: 1 }} />
          <Grid container spacing={1} sx={{ mt: 1 }}>
            <Grid item xs={10}>
              <Typography variant="body2" color="text.secondary" align="right">
                Total excl. VAT (before surcharge):
              </Typography>
            </Grid>
            <GridItem align="right">{totalExclVat?.toFixed(2)} €</GridItem>
          </Grid>
          <Grid container spacing={1} sx={{ mt: 1 }}>
            <Grid item xs={10}>
              <Typography variant="body2" color="text.secondary" align="right">
                Surcharge (%):
              </Typography>
            </Grid>
            <GridItem align="right">0.00 €</GridItem>
          </Grid>
          <Grid container spacing={1} sx={{ mt: 1 }}>
            <Grid item xs={10}>
              <Typography variant="body2" color="text.secondary" align="right">
                Total excl:
              </Typography>
            </Grid>
            <GridItem align="right">{totalExclVat?.toFixed(2)} €</GridItem>
          </Grid>
          <Grid container spacing={1} sx={{ mt: 1 }}>
            <Grid item xs={10}>
              <Box
                display="flex"
                justifyContent="flex-end"
                alignItems="center"
                gap={1}
              >
                <Typography variant="body2" color="text.secondary">
                  VAT ({vatRate}%):
                </Typography>
                {isEditing && availableTaxes.length > 0 && (
                  <Autocomplete
                    size="small"
                    options={availableTaxes}
                    getOptionLabel={(option) =>
                      `${option.name} (${option.percentage}%)`
                    }
                    value={selectedTax}
                    onChange={(_, newValue) => setSelectedTax(newValue)}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        variant="outlined"
                        size="small"
                        sx={{ minWidth: 150 }}
                      />
                    )}
                    isOptionEqualToValue={(option, value) =>
                      option.name === value?.name &&
                      option.percentage === value?.percentage
                    }
                  />
                )}
              </Box>
            </Grid>
            <GridItem align="right">{vatAmount.toFixed(2)} €</GridItem>
          </Grid>
          <Grid container spacing={1} sx={{ mt: 1, fontWeight: "bold" }}>
            <Grid item xs={10}>
              <Typography variant="h6" align="right" fontWeight="bold">
                {t("total")}
              </Typography>
            </Grid>
            <Grid item xs={2}>
              <Typography variant="h6" align="right" fontWeight="bold">
                {totalAmount?.toFixed(2)} €
              </Typography>
            </Grid>
          </Grid>
        </Box>
      </CePaper>
    </Box>
  );
};

export default DetailsSection;
