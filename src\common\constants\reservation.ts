import {
  CancelReservationModalValues,
  DeleteReservationModalValues,
  EditReservationDurationModalValues,
  EditedValues,
  JobStatus,
  ReservationFormValues,
  ReservationFullFormValues,
  StatusColors,
} from "../types";

export const RESERVATION_FORM_VALUES: ReservationFormValues = {
  reservationId: null,
  vehicle: null,
  dateFrom: "",
  dateTo: "",
  siteAddress: "",
  plz: null,
  city: "",
  location: {
    type: "Point",
    coordinates: [0, 0],
  },
  flow: null,
};

export const RESERVATION_FULL_FORM_VALUES: ReservationFullFormValues = {
  orderNumber: "",
  pricelist: null,
  reservationId: null,
  manager: null,
  dispatcher: null,
  operator: null,
  vehicle: null,
  reservationType: null,
  dateFrom: null,
  dateTo: null,
  siteAddress: null,
  city: null,
  plz: null,
  location: {
    type: "Point",
    coordinates: [0, 0],
  },
  clientDetails: {
    name: "",
    lastName: "",
    email: "",
    phoneNumber: "",
    companyName: "",
    companyVatNumber: "",
  },
  job: {
    amountOfConcrete: null,
    balance: false,
    flowRate: null,
    flexiblePipeLength80Mm: null,
    flexiblePipeLength90Mm: null,
    frontOutriggersSpan: null,
    rearOutriggersSpan: null,
    rigidPipeLength100Mm: null,
    rigidPipeLength120Mm: null,
    extraCementBag: false,
    units: null,
    presenceOfPowerLines: false,
    voltage: null,
    pipeStartingFromBAC: false,
    comments: "",
    status: null,
    terrainStability: null,
    tonnageRestriction: false,
    authorizedWeight: null,
    heightRestriction: false,
    heightLimit: null,
    enlistSecondTechnician: false,
    barbotine: false,
    supplyOfTheChemicalSlushie: false,
    parkingOn: null,
    cleaning: null,
    jobType: null,
    ciaw: "",
  },
};

export const RESERVATION_DELETE_DEFAULT: DeleteReservationModalValues = {
  flow: null,
};

export const RESERVATION_CANCEL_DEFAULT: CancelReservationModalValues = {
  flow: null,
  reservationId: null,
  jobId: null,
  reservationTitle: null,
  cancelationFee: null,
};

export const RESERVATION_EDIT_DEFAULT: ReservationFormValues = {
  flow: null,
};

export const RESERVATION_EDIT_DURATION_DEFAULT: EditReservationDurationModalValues =
  {
    flow: null,
  };

export const EMAIL_REGEX_VALIDATION =
  /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,4}$/;

export const INITIAL_EDITED_VALUES: EditedValues = {
  amountOfConcrete: 0,
  flexiblePipeLength80Mm: 0,
  flexiblePipeLength90Mm: 0,
  rigidPipeLength100Mm: 0,
  rigidPipeLength120Mm: 0,
  extraCementBags: false,
  cementBags: 0,
  units: 0,
  secondTechnician: false,
  cleaning: null,
  barbotine: false,
  supplyOfTheChemicalSlushie: false,
};

export const JOB_STEPS = [
  {
    stepIndex: 0,
    progress: 0,
    name: JobStatus.NOT_STARTED,
    label: "common:not-started",
  },
  {
    stepIndex: 1,
    progress: 10,
    name: JobStatus.DRIVING_TO_SITE,
    label: "common:driving-to-site",
  },
  {
    stepIndex: 3,
    progress: 20,
    name: JobStatus.SITE_ARRIVAL,
    label: "common:site-arrival",
  },
  {
    stepIndex: 4,
    progress: 30,
    name: JobStatus.SECURITY_VALIDATION,
    label: "common:security-validation",
  },
  {
    stepIndex: 5,
    progress: 40,
    name: JobStatus.START_SETUP,
    label: "common:start-setup",
  },
  {
    stepIndex: 5,
    progress: 50,
    name: JobStatus.END_SETUP,
    label: "common:end-setup",
  },
  {
    stepIndex: 6,
    progress: 60,
    name: JobStatus.START_PUMPING,
    label: "common:start-pumping",
  },
  {
    stepIndex: 7,
    progress: 70,
    name: JobStatus.END_PUMPING,
    label: "common:end-pumping",
  },
  {
    stepIndex: 8,
    progress: 80,
    name: JobStatus.REPORT,
    label: "common:report",
  },
  {
    stepIndex: 9,
    progress: 90,
    name: JobStatus.SIGNATURE,
    label: "common:signature",
  },
  {
    stepIndex: 10,
    progress: 92,
    name: JobStatus.START_CLEANUP,
    label: "common:start-cleanup",
  },
  {
    stepIndex: 11,
    progress: 94,
    name: JobStatus.END_CLEANUP,
    label: "common:end-cleanup",
  },
  {
    stepIndex: 12,
    progress: 96,
    name: JobStatus.LEAVE_SITE,
    label: "common:leave-site",
  },
  {
    stepIndex: 13,
    progress: 100,
    name: JobStatus.COMPLETE,
    label: "common:complete",
  },
  {
    stepIndex: null,
    progress: null,
    name: JobStatus.CANCELLED,
    label: "common:cancelled",
  },
];

export const STATUS_COLORS: StatusColors = {
  [JobStatus.CANCELLED]: {
    color: "#D32F2F ",
    backgroundColor: "#FEEBEE",
    borderColor: "transparent",
    chipColor: "error",
  },
  [JobStatus.NOT_STARTED]: {
    color: "#000000",
    backgroundColor: "#FFFFFF",
    borderColor: "transparent",
    chipColor: "default",
  },
  [JobStatus.SITE_ARRIVAL]: {
    color: "#0057B2",
    backgroundColor: "#E3F2FD",
    borderColor: "transparent",
    chipColor: "info",
  },
  [JobStatus.DRIVING_TO_SITE]: {
    color: "#F57C00",
    backgroundColor: "#FFF3E0",
    borderColor: "transparent",
    chipColor: "warning",
  },
  [JobStatus.SECURITY_VALIDATION]: {
    color: "#0057B2",
    backgroundColor: "#E3F2FD",
    borderColor: "transparent",
    chipColor: "info",
  },
  [JobStatus.START_SETUP]: {
    color: "#0057B2",
    backgroundColor: "#E3F2FD",
    borderColor: "transparent",
    chipColor: "info",
  },
  [JobStatus.END_SETUP]: {
    color: "#0057B2",
    backgroundColor: "#E3F2FD",
    borderColor: "transparent",
    chipColor: "info",
  },
  [JobStatus.START_PUMPING]: {
    color: "#0057B2",
    backgroundColor: "#E3F2FD",
    borderColor: "transparent",
    chipColor: "info",
  },
  [JobStatus.END_PUMPING]: {
    color: "#0057B2",
    backgroundColor: "#E3F2FD",
    borderColor: "transparent",
    chipColor: "info",
  },
  [JobStatus.REPORT]: {
    color: "#0057B2",
    backgroundColor: "#E3F2FD",
    borderColor: "transparent",
    chipColor: "info",
  },
  [JobStatus.SIGNATURE]: {
    color: "#0057B2",
    backgroundColor: "#E3F2FD",
    borderColor: "transparent",
    chipColor: "info",
  },
  [JobStatus.START_CLEANUP]: {
    color: "#0057B2",
    backgroundColor: "#E3F2FD",
    borderColor: "transparent",
    chipColor: "info",
  },
  [JobStatus.END_CLEANUP]: {
    color: "#0057B2",
    backgroundColor: "#E3F2FD",
    borderColor: "transparent",
    chipColor: "info",
  },
  [JobStatus.LEAVE_SITE]: {
    color: "#0057B2",
    backgroundColor: "#E3F2FD",
    borderColor: "transparent",
    chipColor: "info",
  },
  [JobStatus.COMPLETE]: {
    color: "#388E3C",
    backgroundColor: "#E8F5E9",
    borderColor: "transparent",
    chipColor: "success",
  },
  disabled: {
    color: "#ffffff",
    backgroundColor: "#9e9e9e",
    borderColor: "transparent",
    chipColor: "default",
  },
  default: {
    color: "#0057B2",
    backgroundColor: "#9c27b0",
    borderColor: "transparent",
    chipColor: "default",
  },
};
